import { HttpsError } from 'firebase-functions/lib/v2/providers/https';
import {
    assemblyAIApiKey,
    facebookAppId,
    facebookPageAccessToken,
    facebookVerifyToken,
    getEmailTemplate,
    googleServiceAccountEmail,
    googleServiceAccountKey,
    openAIKey,
    requireAuth,
    sendGridApiKey,
    telegramBotToken,
    twilioAccounSID,
    twilioAuthTpken,
    twilioWhatsappNumber
} from './utils';
import sgMail from '@sendgrid/mail';

export const onValidateFeatures = onCall(
    {
        cors: true,
        secrets: [openAIKey, twilioAuthTpken, twilioAccounSID, twilioWhatsappNumber, facebookVerifyToken, facebookPageAccessToken, telegramBotToken, sendGridApiKey, assemblyAIApiKey, googleServiceAccountEmail, googleServiceAccountKey, facebookAppId],
        cpu: 8,
        memory: '16GiB',
        timeoutSeconds: 3600
    },
    async (request) => {
        const { data, auth } = request;

        if (!auth) {
            throw new HttpsError('unauthenticated', 'Authentication required');
        }

        requireAuth(auth);

        const { feature, toValidate } = data;
        try {
            if (!feature) {
                throw new HttpsError('invalid-argument', 'Feature is required');
            }
            if (!toValidate) {
                throw new HttpsError('invalid-argument', 'Value to validate is required');
            }

            if (feature === 'email') {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!emailRegex.test(toValidate)) {
                    throw new HttpsError('invalid-argument', 'Invalid email format');
                }
                const msg = {
                    to: toValidate, // Recipient's email address
                    from: '<EMAIL>', // Dynamically set the sender email
                    subject: 'Email Validation Test', // Email subject
                    html: 'This is a test email from Liftt. please ignore.'
                };
                sgMail.setApiKey(sendGridApiKey.value());
                await sgMail.send(msg);
            }
        } catch (err) {}
    }
);
