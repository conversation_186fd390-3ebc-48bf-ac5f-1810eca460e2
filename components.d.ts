/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    Accordion: typeof import('primevue/accordion')['default']
    AccordionContent: typeof import('primevue/accordioncontent')['default']
    AccordionHeader: typeof import('primevue/accordionheader')['default']
    AccordionPanel: typeof import('primevue/accordionpanel')['default']
    AccordionTab: typeof import('primevue/accordiontab')['default']
    AutoComplete: typeof import('primevue/autocomplete')['default']
    Avatar: typeof import('primevue/avatar')['default']
    AvatarGroup: typeof import('primevue/avatargroup')['default']
    Badge: typeof import('primevue/badge')['default']
    BestSellingWidget: typeof import('./src/components/dashboard/BestSellingWidget.vue')['default']
    Breadcrumb: typeof import('primevue/breadcrumb')['default']
    Button: typeof import('primevue/button')['default']
    ButtonGroup: typeof import('primevue/buttongroup')['default']
    Calendar: typeof import('primevue/calendar')['default']
    Card: typeof import('primevue/card')['default']
    Carousel: typeof import('primevue/carousel')['default']
    Chart: typeof import('primevue/chart')['default']
    Checkbox: typeof import('primevue/checkbox')['default']
    Chip: typeof import('primevue/chip')['default']
    ColorPicker: typeof import('primevue/colorpicker')['default']
    Column: typeof import('primevue/column')['default']
    ConfirmDialog: typeof import('primevue/confirmdialog')['default']
    ConfirmPopup: typeof import('primevue/confirmpopup')['default']
    ContextMenu: typeof import('primevue/contextmenu')['default']
    DataTable: typeof import('primevue/datatable')['default']
    DataView: typeof import('primevue/dataview')['default']
    DatePicker: typeof import('primevue/datepicker')['default']
    Dialog: typeof import('primevue/dialog')['default']
    Divider: typeof import('primevue/divider')['default']
    Drawer: typeof import('primevue/drawer')['default']
    Dropdown: typeof import('primevue/dropdown')['default']
    Editor: typeof import('primevue/editor')['default']
    FeaturesWidget: typeof import('./src/components/landing/FeaturesWidget.vue')['default']
    Fieldset: typeof import('primevue/fieldset')['default']
    FileUpload: typeof import('primevue/fileupload')['default']
    FloatingConfigurator: typeof import('./src/components/FloatingConfigurator.vue')['default']
    FloatLabel: typeof import('primevue/floatlabel')['default']
    Fluid: typeof import('primevue/fluid')['default']
    FooterWidget: typeof import('./src/components/landing/FooterWidget.vue')['default']
    Galleria: typeof import('primevue/galleria')['default']
    HeroWidget: typeof import('./src/components/landing/HeroWidget.vue')['default']
    HighlightsWidget: typeof import('./src/components/landing/HighlightsWidget.vue')['default']
    IconField: typeof import('primevue/iconfield')['default']
    Image: typeof import('primevue/image')['default']
    InputGroup: typeof import('primevue/inputgroup')['default']
    InputGroupAddon: typeof import('primevue/inputgroupaddon')['default']
    InputIcon: typeof import('primevue/inputicon')['default']
    InputNumber: typeof import('primevue/inputnumber')['default']
    InputSwitch: typeof import('primevue/inputswitch')['default']
    InputText: typeof import('primevue/inputtext')['default']
    Knob: typeof import('primevue/knob')['default']
    Listbox: typeof import('primevue/listbox')['default']
    MegaMenu: typeof import('primevue/megamenu')['default']
    Menu: typeof import('primevue/menu')['default']
    Menubar: typeof import('primevue/menubar')['default']
    Message: typeof import('primevue/message')['default']
    MultiSelect: typeof import('primevue/multiselect')['default']
    NotificationsWidget: typeof import('./src/components/dashboard/NotificationsWidget.vue')['default']
    OrderList: typeof import('primevue/orderlist')['default']
    OverlayBadge: typeof import('primevue/overlaybadge')['default']
    OverlayPanel: typeof import('primevue/overlaypanel')['default']
    Panel: typeof import('primevue/panel')['default']
    PanelMenu: typeof import('primevue/panelmenu')['default']
    Password: typeof import('primevue/password')['default']
    PickList: typeof import('primevue/picklist')['default']
    Popover: typeof import('primevue/popover')['default']
    PricingWidget: typeof import('./src/components/landing/PricingWidget.vue')['default']
    ProgressBar: typeof import('primevue/progressbar')['default']
    ProgressSpinner: typeof import('primevue/progressspinner')['default']
    RadioButton: typeof import('primevue/radiobutton')['default']
    Rating: typeof import('primevue/rating')['default']
    RecentSalesWidget: typeof import('./src/components/dashboard/RecentSalesWidget.vue')['default']
    RevenueStreamWidget: typeof import('./src/components/dashboard/RevenueStreamWidget.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    ScrollPanel: typeof import('primevue/scrollpanel')['default']
    ScrollTop: typeof import('primevue/scrolltop')['default']
    Select: typeof import('primevue/select')['default']
    SelectButton: typeof import('primevue/selectbutton')['default']
    Sidebar: typeof import('primevue/sidebar')['default']
    Skeleton: typeof import('primevue/skeleton')['default']
    Slider: typeof import('primevue/slider')['default']
    SpeedDial: typeof import('primevue/speeddial')['default']
    SplitButton: typeof import('primevue/splitbutton')['default']
    Splitter: typeof import('primevue/splitter')['default']
    SplitterPanel: typeof import('primevue/splitterpanel')['default']
    StatsWidget: typeof import('./src/components/dashboard/StatsWidget.vue')['default']
    Step: typeof import('primevue/step')['default']
    StepList: typeof import('primevue/steplist')['default']
    Stepper: typeof import('primevue/stepper')['default']
    Tab: typeof import('primevue/tab')['default']
    TabList: typeof import('primevue/tablist')['default']
    TabPanel: typeof import('primevue/tabpanel')['default']
    TabPanels: typeof import('primevue/tabpanels')['default']
    Tabs: typeof import('primevue/tabs')['default']
    TabView: typeof import('primevue/tabview')['default']
    Tag: typeof import('primevue/tag')['default']
    Textarea: typeof import('primevue/textarea')['default']
    TieredMenu: typeof import('primevue/tieredmenu')['default']
    Timeline: typeof import('primevue/timeline')['default']
    Toast: typeof import('primevue/toast')['default']
    ToggleButton: typeof import('primevue/togglebutton')['default']
    ToggleSwitch: typeof import('primevue/toggleswitch')['default']
    Toolbar: typeof import('primevue/toolbar')['default']
    TopbarWidget: typeof import('./src/components/landing/TopbarWidget.vue')['default']
    Tree: typeof import('primevue/tree')['default']
    TreeSelect: typeof import('primevue/treeselect')['default']
    TreeTable: typeof import('primevue/treetable')['default']
  }
  export interface ComponentCustomProperties {
    StyleClass: typeof import('primevue/styleclass')['default']
    Tooltip: typeof import('primevue/tooltip')['default']
  }
}
