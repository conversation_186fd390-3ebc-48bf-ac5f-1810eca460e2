import type { Timestamp } from 'firebase/firestore';
import { CompanyInfo } from '@/features/company-info-update';

// Defines the structure for a Tenant object
export interface Tenant extends CompanyInfo {
    id: string; // Unique identifier for the tenant

    // Core company details
    name: string;
    email: string;
    phone: string;
    address: string;
    website?: string;
    ownerId: string; // ID of the user who owns this tenant record
    approved: boolean; // Whether the tenant has been approved
    createdAt: Timestamp | Date; // Timestamp of when the tenant was created
    updatedAt: Timestamp | Date; // Timestamp of the last update
}
