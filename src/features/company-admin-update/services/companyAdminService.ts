import type { User } from '@/entities/user';
import type { CompanyAdminInfo } from '../types';
import { validateEmail, validateRequired, validatePhoneNumber, validateUrl } from '@/utils';

/**
 * Service for handling company admin user operations
 */
export class CompanyAdminService {
    /**
     * Convert CompanyAdminInfo to User format for API updates
     * @param adminInfo - The admin information to convert
     * @returns Partial<User> - Converted user data
     */
    static convertToUserData(adminInfo: Partial<CompanyAdminInfo>): Partial<User> {
        // Build full name from firstName and lastName if provided
        let fullName = adminInfo.name || '';
        if (adminInfo.firstName && adminInfo.lastName) {
            fullName = `${adminInfo.firstName} ${adminInfo.lastName}`.trim();
        } else if (adminInfo.firstName) {
            fullName = adminInfo.firstName;
        } else if (adminInfo.lastName) {
            fullName = adminInfo.lastName;
        }

        // Convert CompanyAdminInfo to User format for API
        const userData: Partial<User> = {
            name: fullName,
            email: adminInfo.email,
            role: adminInfo.role || 'tenant_admin',
            tenantId: adminInfo.tenantId,
            createdAt: new Date()
        };

        // Remove undefined values
        Object.keys(userData).forEach((key) => {
            if (userData[key as keyof typeof userData] === undefined) {
                delete userData[key as keyof typeof userData];
            }
        });

        return userData;
    }

    /**
     * Convert User data to CompanyAdminInfo format for form display
     * @param userData - The user data to convert
     * @returns CompanyAdminInfo - Converted admin info
     */
    static convertFromUserData(userData: User): CompanyAdminInfo {
        // Try to split name into firstName and lastName
        const nameParts = userData.name?.split(' ') || [];
        const firstName = nameParts[0] || '';
        const lastName = nameParts.slice(1).join(' ') || '';

        return {
            name: userData.name,
            firstName,
            lastName,
            email: userData.email,
            role: userData.role,
            tenantId: userData.tenantId,
            createdAt: userData.createdAt,
            // Set defaults for new fields
            jobTitle: '',
            department: '',
            phone: '',
            alternativeEmail: '',
            linkedinUrl: '',
            bio: '',
            timezone: 'UTC',
            language: 'en',
            twoFactorEnabled: false,
            isOwner: true,
            permissions: ['user_management', 'company_settings'],
            onboardingCompleted: false,
            welcomeEmailSent: false
        };
    }

    /**
     * Validate company admin information before saving
     * @param adminInfo - The admin information to validate
     * @returns Promise<{ isValid: boolean; errors: Record<string, string> }>
     */
    static async validateAdminInfo(adminInfo: Partial<CompanyAdminInfo>): Promise<{ 
        isValid: boolean; 
        errors: Record<string, string> 
    }> {
        const errors: Record<string, string> = {};

        // Required field validations
        if (!adminInfo.name?.trim() && (!adminInfo.firstName?.trim() || !adminInfo.lastName?.trim())) {
            if (!adminInfo.firstName?.trim()) {
                errors.firstName = 'First name is required';
            }
            if (!adminInfo.lastName?.trim()) {
                errors.lastName = 'Last name is required';
            }
            if (!adminInfo.name?.trim()) {
                errors.name = 'Full name is required';
            }
        }

        if (!adminInfo.email?.trim()) {
            errors.email = 'Email is required';
        } else {
            // Email format validation
            const emailError = validateEmail(adminInfo.email);
            if (emailError) {
                errors.email = emailError;
            }
        }

        if (!adminInfo.role?.trim()) {
            errors.role = 'Role is required';
        }

        // Optional field validations
        if (adminInfo.phone && adminInfo.phone.trim()) {
            const phoneError = validatePhoneNumber(adminInfo.phone);
            if (phoneError) {
                errors.phone = phoneError;
            }
        }

        if (adminInfo.alternativeEmail && adminInfo.alternativeEmail.trim()) {
            const altEmailError = validateEmail(adminInfo.alternativeEmail);
            if (altEmailError) {
                errors.alternativeEmail = altEmailError;
            }
        }

        if (adminInfo.linkedinUrl && adminInfo.linkedinUrl.trim()) {
            const urlError = validateUrl(adminInfo.linkedinUrl);
            if (urlError) {
                errors.linkedinUrl = urlError;
            }
        }

        // Job title validation
        if (adminInfo.jobTitle && adminInfo.jobTitle.trim().length > 100) {
            errors.jobTitle = 'Job title must be less than 100 characters';
        }

        // Bio validation
        if (adminInfo.bio && adminInfo.bio.trim().length > 500) {
            errors.bio = 'Bio must be less than 500 characters';
        }

        const isValid = Object.keys(errors).length === 0;
        return { isValid, errors };
    }

    /**
     * Generate default permissions based on role
     * @param role - The user role
     * @returns string[] - Array of permission strings
     */
    static getDefaultPermissions(role: string): string[] {
        const permissionMap: Record<string, string[]> = {
            'super_admin': [
                'user_management',
                'company_settings',
                'billing',
                'analytics',
                'api_access',
                'integrations',
                'security',
                'audit_logs',
                'support',
                'data_export'
            ],
            'company_admin': [
                'user_management',
                'company_settings',
                'billing',
                'analytics',
                'integrations',
                'security',
                'data_export'
            ],
            'tenant_admin': [
                'user_management',
                'company_settings',
                'analytics',
                'integrations'
            ],
            'manager': [
                'user_management',
                'analytics'
            ],
            'team_lead': [
                'analytics'
            ]
        };

        return permissionMap[role] || ['analytics'];
    }

    /**
     * Check if the admin info represents a complete onboarding
     * @param adminInfo - The admin information to check
     * @returns boolean - Whether onboarding is complete
     */
    static isOnboardingComplete(adminInfo: Partial<CompanyAdminInfo>): boolean {
        const requiredFields = [
            'name',
            'email',
            'role',
            'jobTitle',
            'department'
        ];

        return requiredFields.every(field => {
            const value = adminInfo[field as keyof CompanyAdminInfo];
            return value && String(value).trim().length > 0;
        });
    }

    /**
     * Generate a welcome message for the admin user
     * @param adminInfo - The admin information
     * @returns string - Welcome message
     */
    static generateWelcomeMessage(adminInfo: CompanyAdminInfo): string {
        const name = adminInfo.firstName || adminInfo.name || 'Admin';
        const jobTitle = adminInfo.jobTitle ? ` as ${adminInfo.jobTitle}` : '';
        
        return `Welcome to the platform, ${name}! We're excited to have you${jobTitle} managing your company's account.`;
    }

    /**
     * Sanitize admin info before saving
     * @param adminInfo - The admin information to sanitize
     * @returns CompanyAdminInfo - Sanitized admin info
     */
    static sanitizeAdminInfo(adminInfo: Partial<CompanyAdminInfo>): Partial<CompanyAdminInfo> {
        const sanitized = { ...adminInfo };

        // Trim string fields
        const stringFields = [
            'name', 'firstName', 'lastName', 'email', 'phone', 'alternativeEmail',
            'jobTitle', 'department', 'linkedinUrl', 'bio', 'timezone', 'language'
        ];

        stringFields.forEach(field => {
            if (sanitized[field as keyof CompanyAdminInfo] && typeof sanitized[field as keyof CompanyAdminInfo] === 'string') {
                (sanitized as any)[field] = (sanitized[field as keyof CompanyAdminInfo] as string).trim();
            }
        });

        // Ensure email is lowercase
        if (sanitized.email) {
            sanitized.email = sanitized.email.toLowerCase();
        }
        if (sanitized.alternativeEmail) {
            sanitized.alternativeEmail = sanitized.alternativeEmail.toLowerCase();
        }

        // Set default values
        if (!sanitized.timezone) {
            sanitized.timezone = 'UTC';
        }
        if (!sanitized.language) {
            sanitized.language = 'en';
        }
        if (!sanitized.role) {
            sanitized.role = 'tenant_admin';
        }

        // Generate permissions if not set
        if (!sanitized.permissions && sanitized.role) {
            sanitized.permissions = this.getDefaultPermissions(sanitized.role);
        }

        return sanitized;
    }
}
