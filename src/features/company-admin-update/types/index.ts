import type { User } from '@/entities/user';

/**
 * Extended admin user information interface for SaaS products
 */
export interface CompanyAdminInfo extends Omit<User, 'id'> {
    // Personal Information
    firstName?: string;
    lastName?: string;
    jobTitle?: string;
    department?: string;
    
    // Contact Information
    phone?: string;
    alternativeEmail?: string;
    
    // Professional Information
    linkedinUrl?: string;
    bio?: string;
    
    // System Information
    timezone?: string;
    language?: string;
    
    // Security & Access
    twoFactorEnabled?: boolean;
    lastLoginAt?: Date;
    
    // Company Context
    tenantId?: string;
    isOwner?: boolean;
    permissions?: string[];
    
    // Onboarding
    onboardingCompleted?: boolean;
    welcomeEmailSent?: boolean;
}

/**
 * Form validation errors interface
 */
export interface CompanyAdminErrors {
    name: string;
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
    jobTitle: string;
    department: string;
    role: string;
    timezone: string;
    language: string;
    linkedinUrl: string;
    bio: string;
    alternativeEmail: string;
}

/**
 * Job title options for dropdown
 */
export const JO<PERSON>_TITLE_OPTIONS = [
    { label: 'CEO / Founder', value: 'ceo' },
    { label: 'CTO / Technical Director', value: 'cto' },
    { label: 'COO / Operations Director', value: 'coo' },
    { label: 'CFO / Finance Director', value: 'cfo' },
    { label: 'VP of Sales', value: 'vp_sales' },
    { label: 'VP of Marketing', value: 'vp_marketing' },
    { label: 'General Manager', value: 'general_manager' },
    { label: 'Product Manager', value: 'product_manager' },
    { label: 'Sales Manager', value: 'sales_manager' },
    { label: 'Marketing Manager', value: 'marketing_manager' },
    { label: 'Operations Manager', value: 'operations_manager' },
    { label: 'IT Manager', value: 'it_manager' },
    { label: 'Business Owner', value: 'business_owner' },
    { label: 'Director', value: 'director' },
    { label: 'Manager', value: 'manager' },
    { label: 'Other', value: 'other' }
];

/**
 * Department options for dropdown
 */
export const DEPARTMENT_OPTIONS = [
    { label: 'Executive', value: 'executive' },
    { label: 'Sales', value: 'sales' },
    { label: 'Marketing', value: 'marketing' },
    { label: 'Operations', value: 'operations' },
    { label: 'Finance', value: 'finance' },
    { label: 'Human Resources', value: 'hr' },
    { label: 'Information Technology', value: 'it' },
    { label: 'Product Development', value: 'product' },
    { label: 'Customer Success', value: 'customer_success' },
    { label: 'Support', value: 'support' },
    { label: 'Legal', value: 'legal' },
    { label: 'Research & Development', value: 'rd' },
    { label: 'Other', value: 'other' }
];

/**
 * Admin role options for dropdown
 */
export const ADMIN_ROLE_OPTIONS = [
    { label: 'Super Admin', value: 'super_admin' },
    { label: 'Company Admin', value: 'company_admin' },
    { label: 'Tenant Admin', value: 'tenant_admin' },
    { label: 'Manager', value: 'manager' },
    { label: 'Team Lead', value: 'team_lead' }
];

/**
 * Timezone options for dropdown (common business timezones)
 */
export const TIMEZONE_OPTIONS = [
    { label: 'UTC', value: 'UTC' },
    { label: 'London (GMT)', value: 'Europe/London' },
    { label: 'New York (EST)', value: 'America/New_York' },
    { label: 'Los Angeles (PST)', value: 'America/Los_Angeles' },
    { label: 'Chicago (CST)', value: 'America/Chicago' },
    { label: 'Toronto (EST)', value: 'America/Toronto' },
    { label: 'Sydney (AEST)', value: 'Australia/Sydney' },
    { label: 'Tokyo (JST)', value: 'Asia/Tokyo' },
    { label: 'Singapore (SGT)', value: 'Asia/Singapore' },
    { label: 'Dubai (GST)', value: 'Asia/Dubai' },
    { label: 'Berlin (CET)', value: 'Europe/Berlin' },
    { label: 'Paris (CET)', value: 'Europe/Paris' },
    { label: 'Amsterdam (CET)', value: 'Europe/Amsterdam' },
    { label: 'Dublin (GMT)', value: 'Europe/Dublin' }
];

/**
 * Language options for dropdown
 */
export const LANGUAGE_OPTIONS = [
    { label: 'English', value: 'en' },
    { label: 'Spanish', value: 'es' },
    { label: 'French', value: 'fr' },
    { label: 'German', value: 'de' },
    { label: 'Italian', value: 'it' },
    { label: 'Portuguese', value: 'pt' },
    { label: 'Dutch', value: 'nl' },
    { label: 'Japanese', value: 'ja' },
    { label: 'Chinese (Simplified)', value: 'zh-CN' },
    { label: 'Chinese (Traditional)', value: 'zh-TW' },
    { label: 'Korean', value: 'ko' },
    { label: 'Arabic', value: 'ar' },
    { label: 'Russian', value: 'ru' },
    { label: 'Hindi', value: 'hi' }
];

/**
 * Permission options for admin users
 */
export const PERMISSION_OPTIONS = [
    { label: 'User Management', value: 'user_management' },
    { label: 'Company Settings', value: 'company_settings' },
    { label: 'Billing & Subscriptions', value: 'billing' },
    { label: 'Analytics & Reports', value: 'analytics' },
    { label: 'API Access', value: 'api_access' },
    { label: 'Integration Management', value: 'integrations' },
    { label: 'Security Settings', value: 'security' },
    { label: 'Audit Logs', value: 'audit_logs' },
    { label: 'Support Access', value: 'support' },
    { label: 'Data Export', value: 'data_export' }
];

/**
 * Form step configuration for multi-step onboarding
 */
export interface FormStep {
    id: string;
    title: string;
    description: string;
    icon: string;
    fields: string[];
}

export const FORM_STEPS: FormStep[] = [
    {
        id: 'personal',
        title: 'Personal Information',
        description: 'Basic personal details',
        icon: 'pi pi-user',
        fields: ['firstName', 'lastName', 'name', 'email']
    },
    {
        id: 'professional',
        title: 'Professional Details',
        description: 'Job title and department',
        icon: 'pi pi-briefcase',
        fields: ['jobTitle', 'department', 'role']
    },
    {
        id: 'contact',
        title: 'Contact Information',
        description: 'Phone and additional contact details',
        icon: 'pi pi-phone',
        fields: ['phone', 'alternativeEmail', 'timezone', 'language']
    },
    {
        id: 'preferences',
        title: 'Preferences & Access',
        description: 'System preferences and permissions',
        icon: 'pi pi-cog',
        fields: ['linkedinUrl', 'bio', 'permissions']
    }
];
